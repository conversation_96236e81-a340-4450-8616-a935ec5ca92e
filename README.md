# Rings Plugin

A Minecraft Spigot plugin that adds magical rings with passive and active abilities.

## Features

- **4 Different Rings** with unique abilities
- **Passive Effects** that are always active when equipped
- **Attack Abilities** with cooldowns (Shift + Left Click)
- **Action Bar Display** showing current ring and cooldown status
- **YAML Database** for persistent ring storage
- **Fully Configurable** rings and messages
- **Custom Model Data** support for resource packs

## Rings

### 1. Speed Ring ⚡
- **Passive:** Speed II at all times
- **Attack:** Leap forward (30 second cooldown)
- **Class:** SPEED

### 2. Strength Ring 💪
- **Passive:** Strength II at all times  
- **Attack:** Strength III for 20 seconds (5 minute cooldown)
- **Class:** STRENGTH

### 3. Lightning Ring ⚡
- **Passive:** Fire Resistance at all times
- **Attack:** Summon lightning where you're looking (2 minute cooldown)
- **Class:** LIGHTNING

### 4. Emerald Ring 💎
- **Passive:** Hero of the Village at all times
- **Attack:** Spawn a weak iron golem (3 minute cooldown)
- **Class:** EMERALD

## How to Use

1. **Equipping Rings:** Right-click a ring item to equip it
2. **Using Abilities:** Shift + Left-click to use the ring's attack ability
3. **Action Bar:** Shows your current ring and cooldown status
4. **Replacing Rings:** Equipping a new ring replaces the old one (no return)

## Commands

- `/rings reload` - Reload the configuration
- `/rings give <player> <ring_name>` - Give a ring to a player
- `/rings list` - List all available rings

**Ring Names:** `speed_ring`, `strength_ring`, `lightning_ring`, `emerald_ring`

## Permissions

- `rings.admin` - Access to all commands (default: op)
- `rings.use` - Ability to use rings (default: true)
- `rings.give` - Ability to give rings (default: op)

## Configuration

All rings are fully configurable in `config.yml`:
- Display names and lore
- Materials and custom model data
- Attack cooldowns
- Ring classes
- Messages

## Installation

1. Download the plugin JAR file
2. Place it in your server's `plugins` folder
3. Restart your server
4. Configure rings in `plugins/Rings/config.yml` if needed

## Requirements

- Spigot/Paper 1.20+
- Java 17+

## Files Created

- `plugins/Rings/config.yml` - Ring configurations and messages
- `plugins/Rings/database.yml` - Player equipped rings storage

## Support

For issues or feature requests, please create an issue on the GitHub repository.
