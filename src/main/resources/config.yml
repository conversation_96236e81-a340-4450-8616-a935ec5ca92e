# Rings Plugin Configuration
# Configure all rings and their properties here

rings:
  speed_ring:
    display_name: "§b⚡ Speed Ring"
    material: "GOLD_INGOT"
    custom_model_data: 1001
    attack_cooldown: 30  # seconds
    class: "SPEED"
    lore:
      - "§7A ring that grants incredible speed"
      - "§7Passive: §bSpeed II"
      - "§7Attack: §bLeap Forward"
      - "§7Cooldown: §e30 seconds"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  strength_ring:
    display_name: "§c💪 Strength Ring"
    material: "IRON_INGOT"
    custom_model_data: 1002
    attack_cooldown: 300  # seconds (5 minutes)
    class: "STRENGTH"
    lore:
      - "§7A ring that enhances physical power"
      - "§7Passive: §cStrength II"
      - "§7Attack: §cStrength III for 20s"
      - "§7Cooldown: §e5 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  lightning_ring:
    display_name: "§e⚡ Lightning Ring"
    material: "DIAMOND"
    custom_model_data: 1003
    attack_cooldown: 120  # seconds (2 minutes)
    class: "LIGHTNING"
    lore:
      - "§7A ring that commands the storms"
      - "§7Passive: §6Fire Resistance"
      - "§7Attack: §eSummon Lightning"
      - "§7Cooldown: §e2 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  emerald_ring:
    display_name: "§a💎 Emerald Ring"
    material: "EMERALD"
    custom_model_data: 1004
    attack_cooldown: 180  # seconds (3 minutes)
    class: "EMERALD"
    lore:
      - "§7A ring blessed by villagers"
      - "§7Passive: §2Hero of the Village"
      - "§7Attack: §2Summon Iron Golem"
      - "§7Cooldown: §e3 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

# Messages sent to players
messages:
  ring_equipped: "§aYou equipped the %ring_name%§a!"
  ring_unequipped: "§cYou unequipped your ring!"
  attack_cooldown: "§cAttack is on cooldown for %time% seconds!"
  no_ring_equipped: "§cYou don't have a ring equipped!"
  ring_not_found: "§cRing not found!"

# Ring Classes (for future expansion)
ring_classes:
  SPEED:
    name: "Speed Class"
    description: "Rings focused on movement and agility"
  STRENGTH:
    name: "Strength Class"
    description: "Rings focused on combat and power"
  LIGHTNING:
    name: "Lightning Class"
    description: "Rings focused on elemental magic"
  EMERALD:
    name: "Emerald Class"
    description: "Rings focused on utility and support"
