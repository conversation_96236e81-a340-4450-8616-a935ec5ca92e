package me.zivush.rings.rings;

import me.zivush.rings.Ring;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.List;

public class StrengthRing extends Ring {
    
    public StrengthRing(String name, String displayName, Material material, int customModelData, 
                        List<String> lore, int attackCooldown, String ringClass) {
        super(name, displayName, material, customModelData, lore, attackCooldown, ringClass);
    }
    
    @Override
    public void applyPassiveEffects(Player player) {
        // Apply Strength 2 effect (duration: 999999 ticks = ~13.8 hours, effectively permanent)
        player.addPotionEffect(new PotionEffect(PotionEffectType.INCREASE_DAMAGE, 999999, 1, false, false));
    }
    
    @Override
    public void removePassiveEffects(Player player) {
        // Remove Strength effect
        player.removePotionEffect(PotionEffectType.INCREASE_DAMAGE);
    }
    
    @Override
    public void executeAttack(Player player) {
        // Apply Strength 3 for 20 seconds (400 ticks)
        player.addPotionEffect(new PotionEffect(PotionEffectType.INCREASE_DAMAGE, 400, 2, false, true));
        player.sendMessage("§cYou feel an overwhelming surge of strength!");
    }
}
