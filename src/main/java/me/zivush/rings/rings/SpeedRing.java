package me.zivush.rings.rings;

import me.zivush.rings.Ring;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import java.util.List;

public class SpeedRing extends Ring {
    
    public SpeedRing(String name, String displayName, Material material, int customModelData, 
                     List<String> lore, int attackCooldown, String ringClass) {
        super(name, displayName, material, customModelData, lore, attackCooldown, ringClass);
    }
    
    @Override
    public void applyPassiveEffects(Player player) {
        // Apply Speed 2 effect (duration: 999999 ticks = ~13.8 hours, effectively permanent)
        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 999999, 1, false, false));
    }
    
    @Override
    public void removePassiveEffects(Player player) {
        // Remove Speed effect
        player.removePotionEffect(PotionEffectType.SPEED);
    }
    
    @Override
    public void executeAttack(Player player) {
        // Leap ability - launch player forward and upward
        Vector direction = player.getLocation().getDirection();
        direction.multiply(2.0); // Forward force
        direction.setY(1.0); // Upward force
        
        player.setVelocity(direction);
        player.sendMessage("§aYou leap forward with the power of speed!");
    }
}
