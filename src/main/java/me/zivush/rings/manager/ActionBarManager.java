package me.zivush.rings.manager;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

public class ActionBarManager {
    
    private final Rings plugin;
    private final RingManager ringManager;
    
    public ActionBarManager(Rings plugin, RingManager ringManager) {
        this.plugin = plugin;
        this.ringManager = ringManager;
        startActionBarTask();
    }
    
    private void startActionBarTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    updateActionBar(player);
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // Update every second (20 ticks)
    }
    
    private void updateActionBar(Player player) {
        if (!ringManager.hasEquippedRing(player)) {
            return; // Don't show action bar if no ring equipped
        }
        
        Ring equippedRing = ringManager.getEquippedRing(player);
        long remainingCooldown = ringManager.getRemainingCooldown(player);
        
        String actionBarMessage;
        if (remainingCooldown > 0) {
            actionBarMessage = String.format("§7Ring: %s §7| §cCooldown: %ds", 
                equippedRing.getDisplayName(), remainingCooldown);
        } else {
            actionBarMessage = String.format("§7Ring: %s §7| §aREADY", 
                equippedRing.getDisplayName());
        }
        
        // Send action bar message
        player.spigot().sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(actionBarMessage));
    }
}
