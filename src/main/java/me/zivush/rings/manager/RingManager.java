package me.zivush.rings.manager;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import me.zivush.rings.rings.*;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class RingManager {
    
    private final Rings plugin;
    private final DatabaseManager databaseManager;
    private final Map<String, Ring> registeredRings;
    private final Map<UUID, Ring> equippedRings;
    private final Map<UUID, Long> attackCooldowns;
    
    public RingManager(Rings plugin, DatabaseManager databaseManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.registeredRings = new HashMap<>();
        this.equippedRings = new ConcurrentHashMap<>();
        this.attackCooldowns = new ConcurrentHashMap<>();
        
        registerDefaultRings();
    }
    
    private void registerDefaultRings() {
        // Register Speed Ring
        registerRing(new SpeedRing(
            "speed_ring",
            plugin.getConfig().getString("rings.speed_ring.display_name", "§bSpeed Ring"),
            Material.valueOf(plugin.getConfig().getString("rings.speed_ring.material", "GOLD_INGOT")),
            plugin.getConfig().getInt("rings.speed_ring.custom_model_data", 1001),
            plugin.getConfig().getStringList("rings.speed_ring.lore"),
            plugin.getConfig().getInt("rings.speed_ring.attack_cooldown", 30),
            plugin.getConfig().getString("rings.speed_ring.class", "SPEED")
        ));
        
        // Register Strength Ring
        registerRing(new StrengthRing(
            "strength_ring",
            plugin.getConfig().getString("rings.strength_ring.display_name", "§cStrength Ring"),
            Material.valueOf(plugin.getConfig().getString("rings.strength_ring.material", "IRON_INGOT")),
            plugin.getConfig().getInt("rings.strength_ring.custom_model_data", 1002),
            plugin.getConfig().getStringList("rings.strength_ring.lore"),
            plugin.getConfig().getInt("rings.strength_ring.attack_cooldown", 300),
            plugin.getConfig().getString("rings.strength_ring.class", "STRENGTH")
        ));
        
        // Register Lightning Ring
        registerRing(new LightningRing(
            "lightning_ring",
            plugin.getConfig().getString("rings.lightning_ring.display_name", "§eLightning Ring"),
            Material.valueOf(plugin.getConfig().getString("rings.lightning_ring.material", "DIAMOND")),
            plugin.getConfig().getInt("rings.lightning_ring.custom_model_data", 1003),
            plugin.getConfig().getStringList("rings.lightning_ring.lore"),
            plugin.getConfig().getInt("rings.lightning_ring.attack_cooldown", 120),
            plugin.getConfig().getString("rings.lightning_ring.class", "LIGHTNING")
        ));
        
        // Register Emerald Ring
        registerRing(new EmeraldRing(
            "emerald_ring",
            plugin.getConfig().getString("rings.emerald_ring.display_name", "§aEmerald Ring"),
            Material.valueOf(plugin.getConfig().getString("rings.emerald_ring.material", "EMERALD")),
            plugin.getConfig().getInt("rings.emerald_ring.custom_model_data", 1004),
            plugin.getConfig().getStringList("rings.emerald_ring.lore"),
            plugin.getConfig().getInt("rings.emerald_ring.attack_cooldown", 180),
            plugin.getConfig().getString("rings.emerald_ring.class", "EMERALD")
        ));
    }
    
    public void registerRing(Ring ring) {
        registeredRings.put(ring.getName(), ring);
    }
    
    public Ring getRing(String name) {
        return registeredRings.get(name);
    }
    
    public Ring getRingFromItem(ItemStack item) {
        for (Ring ring : registeredRings.values()) {
            if (ring.isThisRing(item)) {
                return ring;
            }
        }
        return null;
    }
    
    public void equipRing(Player player, Ring ring) {
        // Remove current ring if equipped
        if (hasEquippedRing(player)) {
            unequipRing(player);
        }
        
        // Equip new ring
        equippedRings.put(player.getUniqueId(), ring);
        databaseManager.saveEquippedRing(player.getUniqueId(), ring.getName());
        ring.applyPassiveEffects(player);
        
        player.sendMessage(plugin.getConfig().getString("messages.ring_equipped", "§aYou equipped the " + ring.getDisplayName() + "§a!"));
    }
    
    public void unequipRing(Player player) {
        Ring currentRing = equippedRings.get(player.getUniqueId());
        if (currentRing != null) {
            currentRing.removePassiveEffects(player);
            equippedRings.remove(player.getUniqueId());
            databaseManager.removeEquippedRing(player.getUniqueId());
            
            player.sendMessage(plugin.getConfig().getString("messages.ring_unequipped", "§cYou unequipped your ring!"));
        }
    }
    
    public boolean hasEquippedRing(Player player) {
        return equippedRings.containsKey(player.getUniqueId());
    }
    
    public Ring getEquippedRing(Player player) {
        return equippedRings.get(player.getUniqueId());
    }
    
    public void executeAttack(Player player) {
        Ring ring = getEquippedRing(player);
        if (ring == null) {
            return;
        }
        
        UUID playerUUID = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Check cooldown
        if (attackCooldowns.containsKey(playerUUID)) {
            long lastAttack = attackCooldowns.get(playerUUID);
            long cooldownTime = ring.getAttackCooldown() * 1000L; // Convert to milliseconds
            
            if (currentTime - lastAttack < cooldownTime) {
                long remainingTime = (cooldownTime - (currentTime - lastAttack)) / 1000;
                player.sendMessage(plugin.getConfig().getString("messages.attack_cooldown", "§cAttack is on cooldown for " + remainingTime + " seconds!"));
                return;
            }
        }
        
        // Execute attack
        ring.executeAttack(player);
        attackCooldowns.put(playerUUID, currentTime);
    }
    
    public long getRemainingCooldown(Player player) {
        Ring ring = getEquippedRing(player);
        if (ring == null) {
            return 0;
        }
        
        UUID playerUUID = player.getUniqueId();
        if (!attackCooldowns.containsKey(playerUUID)) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long lastAttack = attackCooldowns.get(playerUUID);
        long cooldownTime = ring.getAttackCooldown() * 1000L;
        long remaining = cooldownTime - (currentTime - lastAttack);
        
        return Math.max(0, remaining / 1000);
    }
    
    public void loadPlayerRing(Player player) {
        String ringName = databaseManager.getEquippedRing(player.getUniqueId());
        if (ringName != null) {
            Ring ring = getRing(ringName);
            if (ring != null) {
                equippedRings.put(player.getUniqueId(), ring);
                ring.applyPassiveEffects(player);
            }
        }
    }
    
    public void unloadPlayerRing(Player player) {
        Ring ring = equippedRings.get(player.getUniqueId());
        if (ring != null) {
            ring.removePassiveEffects(player);
            equippedRings.remove(player.getUniqueId());
        }
        attackCooldowns.remove(player.getUniqueId());
    }
    
    public Collection<Ring> getAllRings() {
        return registeredRings.values();
    }
}
