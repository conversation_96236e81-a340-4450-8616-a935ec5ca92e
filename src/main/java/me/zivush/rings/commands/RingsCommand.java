package me.zivush.rings.commands;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import me.zivush.rings.manager.RingManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RingsCommand implements CommandExecutor, TabCompleter {
    
    private final Rings plugin;
    private final RingManager ringManager;
    
    public RingsCommand(Rings plugin, RingManager ringManager) {
        this.plugin = plugin;
        this.ringManager = ringManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("rings.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "reload":
                plugin.reloadConfig();
                sender.sendMessage("§aRings configuration reloaded!");
                break;
                
            case "give":
                if (args.length < 3) {
                    sender.sendMessage("§cUsage: /rings give <player> <ring_name>");
                    return true;
                }
                
                Player target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    sender.sendMessage("§cPlayer not found!");
                    return true;
                }
                
                Ring ring = ringManager.getRing(args[2]);
                if (ring == null) {
                    sender.sendMessage("§cRing not found! Available rings: " + String.join(", ", getRingNames()));
                    return true;
                }
                
                ItemStack ringItem = ring.createItem();
                target.getInventory().addItem(ringItem);
                sender.sendMessage("§aGave " + ring.getDisplayName() + " §ato " + target.getName());
                target.sendMessage("§aYou received " + ring.getDisplayName() + "§a!");
                break;
                
            case "list":
                sender.sendMessage("§6Available Rings:");
                for (Ring r : ringManager.getAllRings()) {
                    sender.sendMessage("§7- §e" + r.getName() + " §7(" + r.getDisplayName() + "§7)");
                }
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage("§6=== Rings Commands ===");
        sender.sendMessage("§e/rings reload §7- Reload configuration");
        sender.sendMessage("§e/rings give <player> <ring> §7- Give a ring to a player");
        sender.sendMessage("§e/rings list §7- List all available rings");
    }
    
    private List<String> getRingNames() {
        List<String> names = new ArrayList<>();
        for (Ring ring : ringManager.getAllRings()) {
            names.add(ring.getName());
        }
        return names;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("rings.admin")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            return Arrays.asList("reload", "give", "list");
        }
        
        if (args.length == 2 && args[0].equalsIgnoreCase("give")) {
            List<String> players = new ArrayList<>();
            for (Player player : Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }
            return players;
        }
        
        if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
            return getRingNames();
        }
        
        return new ArrayList<>();
    }
}
