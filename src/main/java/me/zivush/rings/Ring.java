package me.zivush.rings;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;

import java.util.List;

public abstract class Ring {
    
    protected String name;
    protected String displayName;
    protected Material material;
    protected int customModelData;
    protected List<String> lore;
    protected int attackCooldown; // in seconds
    protected String ringClass;
    
    public Ring(String name, String displayName, Material material, int customModelData, 
                List<String> lore, int attackCooldown, String ringClass) {
        this.name = name;
        this.displayName = displayName;
        this.material = material;
        this.customModelData = customModelData;
        this.lore = lore;
        this.attackCooldown = attackCooldown;
        this.ringClass = ringClass;
    }
    
    /**
     * Creates the ItemStack for this ring
     */
    public ItemStack createItem() {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(displayName);
            meta.setLore(lore);
            meta.setCustomModelData(customModelData);
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Apply passive effects when ring is equipped
     */
    public abstract void applyPassiveEffects(Player player);
    
    /**
     * Remove passive effects when ring is unequipped
     */
    public abstract void removePassiveEffects(Player player);
    
    /**
     * Execute the ring's attack ability
     */
    public abstract void executeAttack(Player player);
    
    /**
     * Check if an ItemStack is this ring
     */
    public boolean isThisRing(ItemStack item) {
        if (item == null || item.getType() != material) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasCustomModelData()) {
            return false;
        }
        
        return meta.getCustomModelData() == customModelData && 
               meta.getDisplayName().equals(displayName);
    }
    
    // Getters
    public String getName() { return name; }
    public String getDisplayName() { return displayName; }
    public Material getMaterial() { return material; }
    public int getCustomModelData() { return customModelData; }
    public List<String> getLore() { return lore; }
    public int getAttackCooldown() { return attackCooldown; }
    public String getRingClass() { return ringClass; }
    
    // Setters for configuration
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    public void setMaterial(Material material) { this.material = material; }
    public void setCustomModelData(int customModelData) { this.customModelData = customModelData; }
    public void setLore(List<String> lore) { this.lore = lore; }
    public void setAttackCooldown(int attackCooldown) { this.attackCooldown = attackCooldown; }
    public void setRingClass(String ringClass) { this.ringClass = ringClass; }
}
